from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os
import requests
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///pos_restaurant.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Modèles de base de données
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(60), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class MenuItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    category = db.relationship('Category', backref='menu_items')

class Table(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    number = db.Column(db.String(10), nullable=False)
    status = db.Column(db.String(20), default='disponible')  # disponible, occupée, facturation
    capacity = db.Column(db.Integer, default=4)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    table_id = db.Column(db.Integer, db.ForeignKey('table.id'), nullable=False)
    table_number = db.Column(db.String(10), nullable=False)
    total = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='en_cours')  # en_cours, payée
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    table = db.relationship('Table', backref='orders')
    items = db.relationship('OrderItem', backref='order', cascade='all, delete-orphan')

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    menu_item_id = db.Column(db.Integer, db.ForeignKey('menu_item.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False)
    quantity = db.Column(db.Integer, nullable=False)

    menu_item = db.relationship('MenuItem')

# Routes d'authentification
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']

        # Pour cette démo, on accepte n'importe quel email/mot de passe
        # Dans un vrai projet, vous devriez vérifier les identifiants
        user = User.query.filter_by(email=email).first()
        if not user:
            # Créer un nouvel utilisateur
            user = User(email=email, password=password)
            db.session.add(user)
            db.session.commit()

            # Créer des catégories par défaut
            default_categories = ['Plats', 'Boissons', 'Desserts']
            for cat_name in default_categories:
                category = Category(name=cat_name, user_id=user.id)
                db.session.add(category)

            # Créer des tables par défaut
            for i in range(1, 11):
                table = Table(number=f'T{i}', user_id=user.id)
                db.session.add(table)

            db.session.commit()

        session['user_id'] = user.id
        session['user_email'] = user.email
        flash('Connexion réussie!', 'success')
        return redirect(url_for('dashboard'))

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('Déconnexion réussie!', 'info')
    return redirect(url_for('login'))

# Route principale du tableau de bord
@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']

    # Récupérer les données nécessaires
    categories = Category.query.filter_by(user_id=user_id).all()
    menu_items = MenuItem.query.filter_by(user_id=user_id).all()
    tables = Table.query.filter_by(user_id=user_id).order_by(Table.number).all()

    return render_template('dashboard.html',
                         categories=categories,
                         menu_items=menu_items,
                         tables=tables,
                         user_email=session['user_email'])

# Routes API pour les opérations AJAX
@app.route('/api/menu_items')
def api_menu_items():
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    user_id = session['user_id']
    items = MenuItem.query.filter_by(user_id=user_id).all()

    return jsonify([{
        'id': item.id,
        'name': item.name,
        'description': item.description,
        'price': item.price,
        'category': item.category.name,
        'category_id': item.category_id
    } for item in items])

@app.route('/api/categories')
def api_categories():
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    user_id = session['user_id']
    categories = Category.query.filter_by(user_id=user_id).all()

    return jsonify([{
        'id': cat.id,
        'name': cat.name
    } for cat in categories])

@app.route('/api/tables')
def api_tables():
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    user_id = session['user_id']
    tables = Table.query.filter_by(user_id=user_id).order_by(Table.number).all()

    # Ajouter les informations de commande pour chaque table
    tables_data = []
    for table in tables:
        active_order = Order.query.filter_by(
            table_id=table.id,
            status='en_cours'
        ).first()

        tables_data.append({
            'id': table.id,
            'number': table.number,
            'status': table.status,
            'capacity': table.capacity,
            'order_id': active_order.id if active_order else None
        })

    return jsonify(tables_data)

# Routes API pour les commandes
@app.route('/api/orders', methods=['POST'])
def api_create_order():
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    data = request.get_json()
    user_id = session['user_id']

    try:
        # Vérifier si la table existe et appartient à l'utilisateur
        table = Table.query.filter_by(id=data['table_id'], user_id=user_id).first()
        if not table:
            return jsonify({'error': 'Table non trouvée'}), 404

        # Calculer le total
        total = sum(item['price'] * item['quantity'] for item in data['items'])

        # Vérifier s'il y a déjà une commande en cours pour cette table
        existing_order = Order.query.filter_by(
            table_id=table.id,
            status='en_cours'
        ).first()

        if existing_order:
            # Mettre à jour la commande existante
            existing_order.total = total
            existing_order.updated_at = datetime.utcnow()

            # Supprimer les anciens articles
            OrderItem.query.filter_by(order_id=existing_order.id).delete()

            # Ajouter les nouveaux articles
            for item_data in data['items']:
                order_item = OrderItem(
                    order_id=existing_order.id,
                    menu_item_id=item_data['id'],
                    name=item_data['name'],
                    price=item_data['price'],
                    quantity=item_data['quantity']
                )
                db.session.add(order_item)

            db.session.commit()
            return jsonify({'success': True, 'order_id': existing_order.id, 'action': 'updated'})
        else:
            # Créer une nouvelle commande
            order = Order(
                table_id=table.id,
                table_number=table.number,
                total=total,
                user_id=user_id
            )
            db.session.add(order)
            db.session.flush()  # Pour obtenir l'ID de la commande

            # Ajouter les articles de la commande
            for item_data in data['items']:
                order_item = OrderItem(
                    order_id=order.id,
                    menu_item_id=item_data['id'],
                    name=item_data['name'],
                    price=item_data['price'],
                    quantity=item_data['quantity']
                )
                db.session.add(order_item)

            # Mettre à jour le statut de la table
            table.status = 'occupée'

            db.session.commit()
            return jsonify({'success': True, 'order_id': order.id, 'action': 'created'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/orders/<int:order_id>')
def api_get_order(order_id):
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    user_id = session['user_id']
    order = Order.query.filter_by(id=order_id, user_id=user_id).first()

    if not order:
        return jsonify({'error': 'Commande non trouvée'}), 404

    return jsonify({
        'id': order.id,
        'table_number': order.table_number,
        'total': order.total,
        'status': order.status,
        'items': [{
            'id': item.menu_item_id,
            'name': item.name,
            'price': item.price,
            'quantity': item.quantity
        } for item in order.items]
    })

@app.route('/api/orders/<int:order_id>/pay', methods=['POST'])
def api_pay_order(order_id):
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    user_id = session['user_id']
    order = Order.query.filter_by(id=order_id, user_id=user_id).first()

    if not order:
        return jsonify({'error': 'Commande non trouvée'}), 404

    try:
        # Marquer la commande comme payée
        order.status = 'payée'
        order.updated_at = datetime.utcnow()

        # Mettre à jour le statut de la table
        table = Table.query.get(order.table_id)
        table.status = 'facturation'

        db.session.commit()
        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/tables/<int:table_id>/clear', methods=['POST'])
def api_clear_table(table_id):
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    user_id = session['user_id']
    table = Table.query.filter_by(id=table_id, user_id=user_id).first()

    if not table:
        return jsonify({'error': 'Table non trouvée'}), 404

    try:
        # Remettre la table en statut disponible
        table.status = 'disponible'
        db.session.commit()
        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Routes API pour la gestion du menu
@app.route('/api/menu_items', methods=['POST'])
def api_add_menu_item():
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    data = request.get_json()
    user_id = session['user_id']

    try:
        # Vérifier que la catégorie appartient à l'utilisateur
        category = Category.query.filter_by(id=data['category_id'], user_id=user_id).first()
        if not category:
            return jsonify({'error': 'Catégorie non trouvée'}), 404

        menu_item = MenuItem(
            name=data['name'],
            description=data.get('description', ''),
            price=float(data['price']),
            category_id=data['category_id'],
            user_id=user_id
        )

        db.session.add(menu_item)
        db.session.commit()

        return jsonify({'success': True, 'item_id': menu_item.id})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/menu_items/<int:item_id>', methods=['DELETE'])
def api_delete_menu_item(item_id):
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    user_id = session['user_id']
    menu_item = MenuItem.query.filter_by(id=item_id, user_id=user_id).first()

    if not menu_item:
        return jsonify({'error': 'Article non trouvé'}), 404

    try:
        db.session.delete(menu_item)
        db.session.commit()
        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Route pour la génération de description avec Gemini
@app.route('/api/generate_description', methods=['POST'])
def api_generate_description():
    if 'user_id' not in session:
        return jsonify({'error': 'Non autorisé'}), 401

    data = request.get_json()
    item_name = data.get('name', '')
    category_name = data.get('category', '')

    if not item_name or not category_name:
        return jsonify({'error': 'Nom et catégorie requis'}), 400

    # Configuration de l'API Gemini
    # Option 1: Variable d'environnement (recommandé)
    api_key = os.environ.get('GEMINI_API_KEY', '')

    # Option 2: Clé directement dans le code (pour le développement uniquement)
    # Remplacez 'VOTRE_CLE_API_ICI' par votre vraie clé API Gemini
    if not api_key:
        api_key = 'VOTRE_CLE_API_ICI'  # ⚠️ Remplacez par votre clé API

    if not api_key or api_key == 'VOTRE_CLE_API_ICI':
        return jsonify({'error': 'Clé API Gemini non configurée. Veuillez définir GEMINI_API_KEY ou modifier le code.'}), 500

    prompt = f"Génère une description de menu courte, alléchante et créative (environ 20-30 mots) pour un plat nommé \"{item_name}\" dans la catégorie \"{category_name}\" pour un restaurant. Met l'accent sur les saveurs et les ingrédients clés."

    try:
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={api_key}"
        payload = {
            "contents": [
                {
                    "role": "user",
                    "parts": [{"text": prompt}]
                }
            ]
        }

        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})

        if response.status_code == 200:
            result = response.json()
            if (result.get('candidates') and
                len(result['candidates']) > 0 and
                result['candidates'][0].get('content') and
                result['candidates'][0]['content'].get('parts') and
                len(result['candidates'][0]['content']['parts']) > 0):

                description = result['candidates'][0]['content']['parts'][0]['text'].strip()
                return jsonify({'success': True, 'description': description})
            else:
                return jsonify({'error': 'Réponse inattendue de l\'API Gemini'}), 500
        else:
            return jsonify({'error': f'Erreur API Gemini: {response.status_code}'}), 500

    except Exception as e:
        return jsonify({'error': f'Erreur lors de la génération: {str(e)}'}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
