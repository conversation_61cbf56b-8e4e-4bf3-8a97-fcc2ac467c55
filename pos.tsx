import React, { useState, useEffect, useCallback } from 'react';
import { initializeApp } from 'firebase/app';
import { getAuth, signInWithCustomToken, signInAnonymously, onAuthStateChanged, signOut } from 'firebase/auth';
import { getFirestore, collection, doc, addDoc, getDocs, setDoc, onSnapshot, query, where, updateDoc, deleteDoc, writeBatch } from 'firebase/firestore';
import { setLogLevel } from 'firebase/firestore';

// --- Configuration Firebase ---
// Ces variables globales __app_id, __firebase_config, __initial_auth_token sont fournies par l'environnement Canvas.
const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-pos-app';
const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {
    // Remplacez par votre configuration Firebase si __firebase_config n'est pas disponible
    apiKey: "YOUR_API_KEY",
    authDomain: "YOUR_AUTH_DOMAIN",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_STORAGE_BUCKET",
    messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
    appId: "YOUR_APP_ID"
};
const initialAuthToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : undefined;

// --- Initialisation Firebase ---
const firebaseApp = initializeApp(firebaseConfig);
const auth = getAuth(firebaseApp);
const db = getFirestore(firebaseApp);
setLogLevel('debug'); // 'debug' pour des logs détaillés, 'silent' pour désactiver

// --- Icônes (Lucide React) ---
// Dans un vrai projet, installez avec `npm install lucide-react`
// Pour cette démo, nous allons simuler quelques icônes SVG simples.
const CircleUser = ({ size = 24, className = "" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <circle cx="12" cy="12" r="10" /><circle cx="12" cy="10" r="3" /><path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
    </svg>
);
const LogOut = ({ size = 24, className = "" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" /><polyline points="16 17 21 12 16 7" /><line x1="21" y1="12" x2="9" y2="12" />
    </svg>
);
const Utensils = ({ size = 24, className = "" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2" /><path d="M7 2v20" /><path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Z" />
    </svg>
);
const ListOrdered = ({ size = 24, className = "" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <line x1="10" x2="21" y1="6" y2="6"/><line x1="10" x2="21" y1="12" y2="12"/><line x1="10" x2="21" y1="18" y2="18"/>
        <path d="M4 6h1v4"/><path d="M4 10h2"/><path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"/>
    </svg>
);
const SquareKanban = ({ size = 24, className = "" }) => (
     <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <rect width="18" height="18" x="3" y="3" rx="2"/><path d="M8 7v7"/><path d="M12 7v4"/><path d="M16 7v9"/>
    </svg>
);
const Settings = ({ size = 24, className = "" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 .25 1l-.43.25a2 2 0 0 1-1.73 1L2 12.22v.44a2 2 0 0 0 2 2v.18a2 2 0 0 1 1 1.73l.43.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.38a2 2 0 0 0-.73-2.73l-.15-.1a2 2 0 0 1-.25-1l.43-.25a2 2 0 0 1 1.73-1L22 11.78v-.44a2 2 0 0 0-2-2v-.18a2 2 0 0 1-1-1.73l-.43-.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 .25 1l-.43.25a2 2 0 0 1-1.73 1Z"/><circle cx="12" cy="12" r="3"/>
    </svg>
);
const PlusCircle = ({ size = 24, className = "" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="16"/><line x1="8" y1="12" x2="16" y2="12"/>
    </svg>
);
const Trash2 = ({ size = 24, className = "" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/>
    </svg>
);
const Sparkles = ({ size = 16, className = "" }) => ( // Icône pour Gemini
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
        <path d="M12 2L14.5 8L21 9.5L16.5 14L18 21L12 17.5L6 21L7.5 14L3 9.5L9.5 8L12 2z"/>
        <path d="M5 3v4h4"/>
        <path d="M19 17v4h-4"/>
    </svg>
);


// --- Composants UI ---

// Modal générique
const Modal = ({ isOpen, onClose, title, children }) => {
    if (!isOpen) return null;
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold">{title}</h3>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
                </div>
                {children}
            </div>
        </div>
    );
};

// Écran de connexion (simulation)
const LoginScreen = ({ onLogin }) => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    // Dans une vraie application, vous utiliseriez signInWithEmailAndPassword
    // Ici, nous simulons juste un succès après un délai.
    const handleLogin = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');
        if (!email || !password) {
            setError("Veuillez entrer l'email et le mot de passe.");
            setIsLoading(false);
            return;
        }
        // Simuler une tentative de connexion
        console.log("Tentative de connexion avec (simulé):", email);
        setTimeout(() => {
            // Pour cette démo, on ne vérifie pas les identifiants.
            // onLogin() sera appelé par onAuthStateChanged dans App.js si l'authentification Firebase réussit.
             console.log("Simulation de connexion réussie. L'état d'authentification Firebase devrait gérer la suite.");
             setIsLoading(false);
        }, 1000);
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 to-slate-700 p-4">
            <div className="bg-white p-8 rounded-xl shadow-2xl w-full max-w-md">
                <div className="text-center mb-8">
                    <Utensils size={48} className="mx-auto text-sky-600" />
                    <h1 className="text-3xl font-bold text-slate-800 mt-2">POS Restaurant</h1>
                    <p className="text-slate-600">Connectez-vous pour gérer vos opérations</p>
                </div>
                <form onSubmit={handleLogin}>
                    {error && <p className="bg-red-100 text-red-700 p-3 rounded-md mb-4 text-sm">{error}</p>}
                    <div className="mb-4">
                        <label className="block text-slate-700 text-sm font-semibold mb-2" htmlFor="email">
                            Email
                        </label>
                        <input
                            className="w-full px-4 py-3 rounded-lg border border-slate-300 focus:outline-none focus:ring-2 focus:ring-sky-500 transition duration-150"
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                        />
                    </div>
                    <div className="mb-6">
                        <label className="block text-slate-700 text-sm font-semibold mb-2" htmlFor="password">
                            Mot de passe
                        </label>
                        <input
                            className="w-full px-4 py-3 rounded-lg border border-slate-300 focus:outline-none focus:ring-2 focus:ring-sky-500 transition duration-150"
                            id="password"
                            type="password"
                            placeholder="********"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                        />
                    </div>
                    <button
                        className="w-full bg-sky-600 hover:bg-sky-700 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50"
                        type="submit"
                        disabled={isLoading}
                    >
                        {isLoading ? 'Connexion...' : 'Se connecter'}
                    </button>
                </form>
                 <p className="text-center text-xs text-slate-500 mt-4">
                    Pour cette démo, l'authentification est simulée. L'état d'authentification réel est géré par Firebase (anonyme ou via token).
                </p>
            </div>
        </div>
    );
};


// --- Composants principaux de l'application ---

// Affichage du menu
const MenuDisplay = ({ menuItems, onAddToOrder, categories }) => {
    const [selectedCategory, setSelectedCategory] = useState('Tous');

    const filteredItems = selectedCategory === 'Tous' 
        ? menuItems 
        : menuItems.filter(item => item.category === selectedCategory);

    return (
        <div className="bg-white p-4 rounded-lg shadow h-full overflow-y-auto">
            <h3 className="text-xl font-semibold mb-3 text-slate-700">Menu</h3>
            <div className="mb-4 flex space-x-2 overflow-x-auto pb-2">
                <button 
                    onClick={() => setSelectedCategory('Tous')}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${selectedCategory === 'Tous' ? 'bg-sky-600 text-white' : 'bg-slate-200 text-slate-700 hover:bg-slate-300'}`}
                >
                    Tous
                </button>
                {categories.map(cat => (
                    <button 
                        key={cat}
                        onClick={() => setSelectedCategory(cat)}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap ${selectedCategory === cat ? 'bg-sky-600 text-white' : 'bg-slate-200 text-slate-700 hover:bg-slate-300'}`}
                    >
                        {cat}
                    </button>
                ))}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredItems.length === 0 && <p className="text-slate-500 col-span-full">Aucun article dans cette catégorie.</p>}
                {filteredItems.map(item => (
                    <div key={item.id} className="border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 className="text-md font-semibold text-slate-800">{item.name}</h4>
                        <p className="text-sm text-slate-600">{item.description || 'Pas de description'}</p>
                        <p className="text-lg font-bold text-sky-600 my-2">{item.price?.toFixed(2)} €</p>
                        <button
                            onClick={() => onAddToOrder(item)}
                            className="w-full bg-sky-500 hover:bg-sky-600 text-white text-sm py-2 px-3 rounded-md transition-colors flex items-center justify-center"
                        >
                            <PlusCircle size={16} className="mr-1" /> Ajouter
                        </button>
                    </div>
                ))}
            </div>
        </div>
    );
};

// Panier de commande
const OrderCart = ({ currentOrder, onUpdateQuantity, onRemoveItem, onPlaceOrder, selectedTable, tables }) => {
    const calculateTotal = () => {
        return currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    };

    if (!selectedTable) {
        return (
            <div className="bg-white p-4 rounded-lg shadow h-full flex flex-col justify-center items-center">
                <SquareKanban size={48} className="text-slate-400 mb-4" />
                <p className="text-slate-600 text-center">Veuillez sélectionner une table pour commencer une commande.</p>
            </div>
        );
    }
    
    const tableDetails = tables.find(t => t.id === selectedTable.id);

    return (
        <div className="bg-white p-4 rounded-lg shadow h-full flex flex-col">
            <h3 className="text-xl font-semibold mb-3 text-slate-700">Commande pour Table {selectedTable.number}</h3>
            {tableDetails?.status === 'occupée' && tableDetails.orderId && (
                 <p className="text-sm text-amber-600 bg-amber-100 p-2 rounded-md mb-3">Cette table a une commande en cours (ID: {tableDetails.orderId.substring(0,6)}...). Ajout d'articles modifiera la commande existante.</p>
            )}
            {currentOrder.length === 0 ? (
                <p className="text-slate-500 flex-grow flex items-center justify-center">Le panier est vide.</p>
            ) : (
                <div className="flex-grow overflow-y-auto pr-1">
                    {currentOrder.map(item => (
                        <div key={item.id} className="flex justify-between items-center border-b border-slate-200 py-3">
                            <div>
                                <h5 className="font-medium text-slate-800">{item.name}</h5>
                                <p className="text-sm text-slate-500">{item.price?.toFixed(2)} €</p>
                            </div>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="number"
                                    min="1"
                                    value={item.quantity}
                                    onChange={(e) => onUpdateQuantity(item.id, parseInt(e.target.value))}
                                    className="w-16 text-center border border-slate-300 rounded-md p-1"
                                />
                                <button onClick={() => onRemoveItem(item.id)} className="text-red-500 hover:text-red-700">
                                    <Trash2 size={18} />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            )}
            <div className="mt-auto pt-4 border-t border-slate-200">
                <div className="flex justify-between items-center text-lg font-semibold mb-3">
                    <span>Total:</span>
                    <span className="text-sky-600">{calculateTotal().toFixed(2)} €</span>
                </div>
                <button
                    onClick={onPlaceOrder}
                    disabled={currentOrder.length === 0}
                    className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md transition-colors disabled:opacity-50"
                >
                    {tableDetails?.status === 'occupée' && tableDetails.orderId ? 'Mettre à jour la commande' : 'Passer la commande'}
                </button>
            </div>
        </div>
    );
};

// Grille des tables
const TableGrid = ({ tables, onSelectTable, selectedTableId, onClearTable, onBillTable }) => {
    const getStatusColor = (status) => {
        switch (status) {
            case 'disponible': return 'bg-green-400 hover:bg-green-500';
            case 'occupée': return 'bg-amber-400 hover:bg-amber-500';
            case 'facturation': return 'bg-red-400 hover:bg-red-500';
            default: return 'bg-slate-300 hover:bg-slate-400';
        }
    };

    return (
        <div className="bg-white p-4 rounded-lg shadow h-full overflow-y-auto">
            <h3 className="text-xl font-semibold mb-3 text-slate-700">Tables</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                {tables.map(table => (
                    <div key={table.id}
                        className={`p-3 rounded-lg cursor-pointer transition-all duration-150 ease-in-out transform hover:scale-105
                                    ${getStatusColor(table.status)} 
                                    ${selectedTableId === table.id ? 'ring-4 ring-sky-500 ring-offset-2' : ''}`}
                        onClick={() => onSelectTable(table)}
                    >
                        <div className="text-center">
                            <p className="font-bold text-lg text-white">Table {table.number}</p>
                            <p className="text-xs text-slate-100 capitalize">{table.status}</p>
                            {table.orderId && <p className="text-xs text-slate-200 mt-1">ID: {table.orderId.substring(0,4)}...</p>}
                        </div>
                        {table.status === 'occupée' && (
                             <button 
                                onClick={(e) => { e.stopPropagation(); onBillTable(table); }}
                                className="mt-2 w-full bg-sky-600 hover:bg-sky-700 text-white text-xs py-1 px-2 rounded transition-colors"
                            >
                                Facturer
                            </button>
                        )}
                         {table.status === 'facturation' && (
                             <button 
                                onClick={(e) => { e.stopPropagation(); onClearTable(table.id); }}
                                className="mt-2 w-full bg-red-600 hover:bg-red-700 text-white text-xs py-1 px-2 rounded transition-colors"
                            >
                                Libérer
                            </button>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

// Panneau d'administration (très basique pour la gestion du menu et des tables)
const AdminPanel = ({ userId, initialMenuItems, initialTablesConfig }) => {
    const [menuItems, setMenuItems] = useState(initialMenuItems || []);
    const [tablesConfig, setTablesConfig] = useState(initialTablesConfig || { count: 10, prefix: "T" });
    
    const [newItemName, setNewItemName] = useState('');
    const [newItemPrice, setNewItemPrice] = useState('');
    const [newItemCategory, setNewItemCategory] = useState('');
    const [newItemDescription, setNewItemDescription] = useState('');
    const [categories, setCategories] = useState([]);

    const [isLoadingMenu, setIsLoadingMenu] = useState(false);
    const [isLoadingTables, setIsLoadingTables] = useState(false);
    const [isGeneratingDescription, setIsGeneratingDescription] = useState(false); // Nouvel état pour Gemini
    const [message, setMessage] = useState({ type: '', text: '' });

    const menuCollectionPath = `artifacts/${appId}/users/${userId}/menuItems`;
    const tablesConfigPath = `artifacts/${appId}/users/${userId}/restaurantConfig/tables`;
    const categoriesPath = `artifacts/${appId}/users/${userId}/restaurantConfig/categories`;

    // Charger les catégories
    useEffect(() => {
        const catDocRef = doc(db, categoriesPath);
        const unsubscribe = onSnapshot(catDocRef, (docSnap) => {
            if (docSnap.exists()) {
                setCategories(docSnap.data().list || []);
            } else {
                setCategories(['Plats', 'Boissons', 'Desserts']); // Default
            }
        }, (error) => {
            console.error("Erreur de chargement des catégories:", error);
            setMessage({ type: 'error', text: 'Erreur de chargement des catégories.' });
        });
        return () => unsubscribe();
    }, [userId, categoriesPath]);

    const handleAddCategory = async (newCategory) => {
        if (newCategory && !categories.includes(newCategory)) {
            const updatedCategories = [...categories, newCategory];
            try {
                await setDoc(doc(db, categoriesPath), { list: updatedCategories });
                setCategories(updatedCategories);
                setMessage({ type: 'success', text: 'Catégorie ajoutée.' });
            } catch (error) {
                console.error("Erreur d'ajout de catégorie:", error);
                setMessage({ type: 'error', text: 'Erreur d\'ajout de catégorie.' });
            }
        }
    };
    
    const handleRemoveCategory = async (categoryToRemove) => {
        const updatedCategories = categories.filter(cat => cat !== categoryToRemove);
        try {
            await setDoc(doc(db, categoriesPath), { list: updatedCategories });
            setCategories(updatedCategories);
            setMessage({ type: 'success', text: 'Catégorie supprimée.' });
        } catch (error) {
            console.error("Erreur de suppression de catégorie:", error);
            setMessage({ type: 'error', text: 'Erreur de suppression de catégorie.' });
        }
    };

    // Charger les items du menu
    useEffect(() => {
        if (!userId) return;
        setIsLoadingMenu(true);
        const q = query(collection(db, menuCollectionPath));
        const unsubscribe = onSnapshot(q, (querySnapshot) => {
            const items = [];
            querySnapshot.forEach((doc) => {
                items.push({ id: doc.id, ...doc.data() });
            });
            setMenuItems(items);
            setIsLoadingMenu(false);
        }, (error) => {
            console.error("Erreur de chargement du menu: ", error);
            setMessage({ type: 'error', text: 'Erreur de chargement du menu.' });
            setIsLoadingMenu(false);
        });
        return () => unsubscribe();
    }, [userId, menuCollectionPath]);

    // Charger la configuration des tables
    useEffect(() => {
        if (!userId) return;
        setIsLoadingTables(true);
        const docRef = doc(db, tablesConfigPath);
        const unsubscribe = onSnapshot(docRef, (docSnap) => {
            if (docSnap.exists()) {
                setTablesConfig(docSnap.data());
            } else {
                setDoc(docRef, { count: 10, prefix: "T" })
                    .then(() => setTablesConfig({ count: 10, prefix: "T" }))
                    .catch(e => console.error("Erreur init config tables:", e));
            }
            setIsLoadingTables(false);
        }, (error) => {
            console.error("Erreur de chargement de la config tables: ", error);
            setMessage({ type: 'error', text: 'Erreur de chargement de la config tables.' });
            setIsLoadingTables(false);
        });
        return () => unsubscribe();
    }, [userId, tablesConfigPath]);

    const handleAddMenuItem = async (e) => {
        e.preventDefault();
        if (!newItemName || !newItemPrice || !newItemCategory) {
            setMessage({ type: 'error', text: 'Nom, prix et catégorie sont requis.' });
            return;
        }
        setIsLoadingMenu(true);
        try {
            await addDoc(collection(db, menuCollectionPath), {
                name: newItemName,
                price: parseFloat(newItemPrice),
                category: newItemCategory,
                description: newItemDescription,
                createdAt: new Date(),
            });
            setNewItemName(''); setNewItemPrice(''); setNewItemCategory(''); setNewItemDescription('');
            setMessage({ type: 'success', text: 'Article ajouté au menu.' });
        } catch (error) {
            console.error("Erreur d'ajout d'article: ", error);
            setMessage({ type: 'error', text: "Erreur d\'ajout d\'article." });
        }
        setIsLoadingMenu(false);
    };

    const handleDeleteMenuItem = async (itemId) => {
        const userConfirmed = await new Promise(resolve => {
            if (typeof window !== 'undefined' && window.confirm) {
                 resolve(window.confirm("Êtes-vous sûr de vouloir supprimer cet article ?"));
            } else {
                console.warn("window.confirm non disponible, suppression annulée par défaut. Implémentez un modal personnalisé.");
                resolve(false); 
            }
        });
        if (!userConfirmed) return;
        setIsLoadingMenu(true);
        try {
            await deleteDoc(doc(db, menuCollectionPath, itemId));
            setMessage({ type: 'success', text: 'Article supprimé.' });
        } catch (error) {
            console.error("Erreur de suppression d'article: ", error);
            setMessage({ type: 'error', text: "Erreur de suppression d\'article." });
        }
        setIsLoadingMenu(false);
    };
    
    const handleUpdateTablesConfig = async (e) => {
        e.preventDefault();
        setIsLoadingTables(true);
        try {
            const newCount = parseInt(tablesConfig.count);
            if (isNaN(newCount) || newCount <= 0 || newCount > 100) { 
                setMessage({type: 'error', text: 'Nombre de tables invalide (1-100).'});
                setIsLoadingTables(false);
                return;
            }
            await setDoc(doc(db, tablesConfigPath), { ...tablesConfig, count: newCount });
            const tablesCollectionRefPath = `artifacts/${appId}/users/${userId}/tables`; 
            const q = query(collection(db, tablesCollectionRefPath)); 
            const existingTablesSnapshot = await getDocs(q);
            const batch = writeBatch(db);
            existingTablesSnapshot.forEach(doc => {
                batch.delete(doc.ref);
            });
            for (let i = 1; i <= newCount; i++) {
                const tableDocRef = doc(collection(db, tablesCollectionRefPath)); 
                batch.set(tableDocRef, {
                    number: `${tablesConfig.prefix || 'T'}${i}`,
                    status: 'disponible', 
                    orderId: null,
                    capacity: 4 
                });
            }
            await batch.commit();
            setMessage({ type: 'success', text: 'Configuration des tables mise à jour et tables réinitialisées.' });
        } catch (error) {
            console.error("Erreur de mise à jour de la config tables: ", error);
            setMessage({ type: 'error', text: "Erreur de mise à jour de la config tables." });
        }
        setIsLoadingTables(false);
    };

    // --- Fonctionnalité Gemini API ---
    const handleGenerateDescription = async () => {
        if (!newItemName || !newItemCategory) {
            setMessage({ type: 'error', text: "Veuillez d'abord renseigner le nom et la catégorie de l'article." });
            return;
        }
        setIsGeneratingDescription(true);
        setMessage({ type: '', text: '' });

        const prompt = `Génère une description de menu courte, alléchante et créative (environ 20-30 mots) pour un plat nommé "${newItemName}" dans la catégorie "${newItemCategory}" pour un restaurant. Met l'accent sur les saveurs et les ingrédients clés.`;
        
        let chatHistory = [];
        chatHistory.push({ role: "user", parts: [{ text: prompt }] });
        const payload = { contents: chatHistory };
        const apiKey = ""; // API Key gérée par l'environnement Canvas
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Erreur API Gemini:', errorData);
                throw new Error(`Erreur de l'API Gemini: ${errorData?.error?.message || response.statusText}`);
            }

            const result = await response.json();

            if (result.candidates && result.candidates.length > 0 &&
                result.candidates[0].content && result.candidates[0].content.parts &&
                result.candidates[0].content.parts.length > 0) {
                const text = result.candidates[0].content.parts[0].text;
                setNewItemDescription(text.trim());
                setMessage({ type: 'success', text: 'Description générée !' });
            } else {
                console.error('Réponse inattendue de API Gemini:', result);
                throw new Error("La réponse de l'API Gemini n'a pas le format attendu.");
            }
        } catch (error) {
            console.error("Erreur lors de la génération de la description:", error);
            setMessage({ type: 'error', text: `Erreur de génération: ${error.message}` });
        } finally {
            setIsGeneratingDescription(false);
        }
    };


    return (
        <div className="p-6 bg-slate-50 min-h-full overflow-y-auto">
            <h2 className="text-2xl font-semibold mb-6 text-slate-800">Panneau d'Administration</h2>

            {message.text && (
                <div className={`p-3 rounded-md mb-4 text-sm ${message.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                    {message.text}
                </div>
            )}

            <section className="mb-8 p-6 bg-white rounded-lg shadow">
                <h3 className="text-xl font-semibold mb-4 text-sky-700">Gérer le Menu</h3>
                <form onSubmit={handleAddMenuItem} className="space-y-4 mb-6">
                    <input type="text" placeholder="Nom de l'article" value={newItemName} onChange={e => setNewItemName(e.target.value)} className="w-full p-2 border rounded-md" required />
                    <input type="number" placeholder="Prix (€)" value={newItemPrice} onChange={e => setNewItemPrice(e.target.value)} className="w-full p-2 border rounded-md" step="0.01" required />
                    <select value={newItemCategory} onChange={e => setNewItemCategory(e.target.value)} className="w-full p-2 border rounded-md" required>
                        <option value="">Choisir catégorie</option>
                        {categories.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                    </select>
                    <div className="relative">
                        <textarea 
                            placeholder="Description (optionnel)" 
                            value={newItemDescription} 
                            onChange={e => setNewItemDescription(e.target.value)} 
                            className="w-full p-2 border rounded-md min-h-[80px]" 
                        />
                        <button
                            type="button"
                            onClick={handleGenerateDescription}
                            disabled={isGeneratingDescription || !newItemName || !newItemCategory}
                            title="Générer une description avec l'IA"
                            className="absolute top-2 right-2 bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded-md text-xs flex items-center disabled:opacity-50 transition-colors"
                        >
                            <Sparkles size={14} className="mr-1" /> 
                            {isGeneratingDescription ? "Génération..." : "✨ Générer Description"}
                        </button>
                    </div>
                    <button type="submit" disabled={isLoadingMenu || isGeneratingDescription} className="w-full bg-sky-600 text-white py-2 px-4 rounded-md hover:bg-sky-700 transition disabled:opacity-50">
                        {isLoadingMenu ? "Ajout..." : "Ajouter l'article"}
                    </button>
                </form>
                <div className="max-h-60 overflow-y-auto">
                    {isLoadingMenu && <p>Chargement du menu...</p>}
                    {!isLoadingMenu && menuItems.length === 0 && <p>Aucun article dans le menu.</p>}
                    <ul className="space-y-2">
                        {menuItems.map(item => (
                            <li key={item.id} className="flex justify-between items-center p-3 bg-slate-50 rounded-md shadow-sm">
                                <div>
                                    <span className="font-medium">{item.name}</span> ({item.category}) - {item.price?.toFixed(2)}€
                                    {item.description && <p className="text-xs text-slate-500">{item.description}</p>}
                                </div>
                                <button onClick={() => handleDeleteMenuItem(item.id)} disabled={isLoadingMenu} className="text-red-500 hover:text-red-700 disabled:opacity-50">
                                    <Trash2 size={18} />
                                </button>
                            </li>
                        ))}
                    </ul>
                </div>
            </section>

            <section className="mb-8 p-6 bg-white rounded-lg shadow">
                <h3 className="text-xl font-semibold mb-4 text-sky-700">Gérer les Catégories</h3>
                <div className="flex mb-4">
                    <input 
                        type="text" 
                        placeholder="Nouvelle catégorie" 
                        id="newCategoryInput"
                        className="p-2 border rounded-l-md flex-grow" 
                    />
                    <button 
                        onClick={() => {
                            const input = document.getElementById('newCategoryInput');
                            if (input && input.value) {
                                handleAddCategory(input.value.trim()); 
                                input.value = '';
                            }
                        }}
                        className="bg-sky-500 text-white py-2 px-4 rounded-r-md hover:bg-sky-600 transition"
                    >
                        Ajouter
                    </button>
                </div>
                <div className="space-y-2 max-h-40 overflow-y-auto"> 
                    {categories.length === 0 && <p className="text-slate-500">Aucune catégorie définie.</p>}
                    {categories.map(cat => (
                        <div key={cat} className="flex justify-between items-center p-2 bg-slate-50 rounded-md">
                            <span>{cat}</span>
                            <button onClick={() => handleRemoveCategory(cat)} className="text-red-500 hover:text-red-700">
                                <Trash2 size={16} />
                            </button>
                        </div>
                    ))}
                </div>
            </section>

            <section className="p-6 bg-white rounded-lg shadow">
                <h3 className="text-xl font-semibold mb-4 text-sky-700">Configurer les Tables</h3>
                <form onSubmit={handleUpdateTablesConfig} className="space-y-4">
                    <div>
                        <label htmlFor="tableCount" className="block text-sm font-medium text-slate-700">Nombre de tables:</label>
                        <input 
                            type="number" 
                            id="tableCount"
                            value={tablesConfig.count === undefined ? '' : tablesConfig.count} 
                            onChange={e => setTablesConfig(prev => ({...prev, count: e.target.value === '' ? '' : parseInt(e.target.value) }))} 
                            className="mt-1 p-2 border rounded-md w-full" 
                            min="1" max="100"
                        />
                    </div>
                    <div>
                        <label htmlFor="tablePrefix" className="block text-sm font-medium text-slate-700">Préfixe des tables (ex: T, A):</label>
                        <input 
                            type="text" 
                            id="tablePrefix"
                            value={tablesConfig.prefix === undefined ? '' : tablesConfig.prefix} 
                            onChange={e => setTablesConfig(prev => ({...prev, prefix: e.target.value }))} 
                            className="mt-1 p-2 border rounded-md w-full" 
                        />
                    </div>
                    <button type="submit" disabled={isLoadingTables} className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition disabled:opacity-50">
                        {isLoadingTables ? "Mise à jour..." : "Mettre à jour et Réinitialiser les Tables"}
                    </button>
                </form>
                <p className="text-xs text-amber-700 mt-2 bg-amber-100 p-2 rounded-md">Attention: Mettre à jour la configuration réinitialisera toutes les tables et leurs statuts actuels.</p>
            </section>
        </div>
    );
};


// Composant principal du tableau de bord
const DashboardScreen = ({ user, onLogout }) => {
    const [currentView, setCurrentView] = useState('pos'); 
    const [menuItems, setMenuItems] = useState([]);
    const [categories, setCategories] = useState([]);
    const [tables, setTables] = useState([]);
    const [selectedTable, setSelectedTable] = useState(null); 
    const [currentOrder, setCurrentOrder] = useState([]); 
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [isBillModalOpen, setIsBillModalOpen] = useState(false);
    const [billDetails, setBillDetails] = useState(null); 
    const [toastMessage, setToastMessage] = useState(''); 

    const userId = user.uid;
    const menuCollectionPath = `artifacts/${appId}/users/${userId}/menuItems`;
    const tablesCollectionPath = `artifacts/${appId}/users/${userId}/tables`;
    const ordersCollectionPath = `artifacts/${appId}/users/${userId}/orders`;
    const categoriesPath = `artifacts/${appId}/users/${userId}/restaurantConfig/categories`;
    const tablesConfigPath = `artifacts/${appId}/users/${userId}/restaurantConfig/tables`;

    const showToast = (message) => {
        setToastMessage(message);
        setTimeout(() => {
            setToastMessage('');
        }, 3000); 
    };

    useEffect(() => {
        const catDocRef = doc(db, categoriesPath);
        const unsubscribe = onSnapshot(catDocRef, (docSnap) => {
            if (docSnap.exists()) {
                setCategories(docSnap.data().list || []);
            } else {
                setDoc(catDocRef, { list: ['Plats', 'Boissons', 'Desserts']}) 
                    .then(() => setCategories(['Plats', 'Boissons', 'Desserts']))
                    .catch(e => {
                        console.error("Erreur init catégories:", e);
                        setError('Erreur initialisation des catégories.');
                    });
            }
        }, (error) => {
            console.error("Erreur de chargement des catégories:", error);
            setError('Erreur de chargement des catégories.');
        });
        return () => unsubscribe();
    }, [userId, categoriesPath]); 


    useEffect(() => {
        setIsLoading(true); 
        const q = query(collection(db, menuCollectionPath));
        const unsubscribe = onSnapshot(q, (querySnapshot) => {
            const items = [];
            querySnapshot.forEach((doc) => {
                items.push({ id: doc.id, ...doc.data() });
            });
            setMenuItems(items);
        }, (err) => {
            console.error("Erreur chargement menu:", err);
            setError("Impossible de charger le menu.");
            setIsLoading(false); 
        });
        return () => unsubscribe();
    }, [userId, menuCollectionPath]);

    useEffect(() => {
        const tablesConfigRef = doc(db, tablesConfigPath);
        const unsubConfig = onSnapshot(tablesConfigRef, async (configSnap) => {
            let tableCount = 10; 
            let tablePrefix = "T"; 
            if (configSnap.exists()) {
                tableCount = configSnap.data().count || 10;
                tablePrefix = configSnap.data().prefix || "T";
            } else {
                try {
                    await setDoc(tablesConfigRef, { count: tableCount, prefix: tablePrefix });
                } catch (e) { console.error("Erreur init config tables:", e); }
            }
            const qTables = query(collection(db, tablesCollectionPath));
            const unsubTables = onSnapshot(qTables, async (tablesSnapshot) => {
                const fetchedTables = [];
                tablesSnapshot.forEach((doc) => {
                    fetchedTables.push({ id: doc.id, ...doc.data() });
                });
                if (tablesSnapshot.empty && tableCount > 0 && fetchedTables.length === 0) { 
                    const batch = writeBatch(db);
                    for (let i = 1; i <= tableCount; i++) {
                        const tableDocRef = doc(collection(db, tablesCollectionPath));
                        batch.set(tableDocRef, {
                            number: `${tablePrefix}${i}`,
                            status: 'disponible',
                            orderId: null,
                            capacity: 4 
                        });
                    }
                    try {
                        await batch.commit();
                        console.log("Tables initialisées car la collection était vide.");
                    } catch (e) {
                        console.error("Erreur initialisation tables:", e);
                        setError("Erreur initialisation des tables.");
                    }
                } else {
                     fetchedTables.sort((a, b) => {
                        const numA = parseInt(a.number.replace(/\D/g, ''), 10);
                        const numB = parseInt(b.number.replace(/\D/g, ''), 10);
                        if (isNaN(numA) || isNaN(numB)) return a.number.localeCompare(b.number);
                        return numA - numB;
                    });
                    setTables(fetchedTables);
                }
                setIsLoading(false); 
            }, (err) => {
                console.error("Erreur chargement tables:", err);
                setError("Impossible de charger les tables.");
                setIsLoading(false); 
            });
            return () => unsubTables();
        }, (err) => {
            console.error("Erreur chargement config tables:", err);
            setError("Impossible de charger la configuration des tables.");
            setIsLoading(false); 
        });
        return () => unsubConfig();
    }, [userId, tablesCollectionPath, tablesConfigPath]); 


    const handleSelectTable = useCallback(async (table) => {
        setSelectedTable(table);
        setCurrentOrder([]); 
        if (table.status === 'occupée' && table.orderId) {
            try {
                const orderRef = doc(db, ordersCollectionPath, table.orderId);
                const orderSnap = await getDoc(orderRef); 
                if (orderSnap.exists()) {
                    const orderData = orderSnap.data();
                    setCurrentOrder(orderData.items || []);
                } else {
                    console.warn(`Commande ${table.orderId} non trouvée pour la table ${table.number}`);
                    showToast(`Commande non trouvée pour la table ${table.number}.`);
                    await updateDoc(doc(db, tablesCollectionPath, table.id), { status: 'disponible', orderId: null });
                }
            } catch (e) {
                console.error("Erreur chargement commande existante:", e);
                showToast("Erreur chargement de la commande existante.");
            }
        }
    }, [userId, ordersCollectionPath, tablesCollectionPath, showToast]);


    const handleAddToOrder = (item) => {
        if (!selectedTable) {
            showToast("Veuillez d'abord sélectionner une table.");
            return;
        }
        setCurrentOrder(prevOrder => {
            const existingItem = prevOrder.find(orderItem => orderItem.id === item.id);
            if (existingItem) {
                return prevOrder.map(orderItem =>
                    orderItem.id === item.id ? { ...orderItem, quantity: orderItem.quantity + 1 } : orderItem
                );
            }
            return [...prevOrder, { ...item, quantity: 1 }];
        });
    };

    const handleUpdateQuantity = (itemId, quantity) => {
        const numQuantity = parseInt(quantity);
        if (isNaN(numQuantity) || numQuantity < 1) { 
            handleRemoveItem(itemId);
            return;
        }
        setCurrentOrder(prevOrder =>
            prevOrder.map(item => (item.id === itemId ? { ...item, quantity: numQuantity } : item))
        );
    };

    const handleRemoveItem = (itemId) => {
        setCurrentOrder(prevOrder => prevOrder.filter(item => item.id !== itemId));
    };

    const handlePlaceOrder = async () => {
        if (!selectedTable || currentOrder.length === 0) {
            showToast("Sélectionnez une table et ajoutez des articles.");
            return;
        }
        const orderTotal = currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const orderData = {
            tableId: selectedTable.id,
            tableNumber: selectedTable.number,
            items: currentOrder,
            total: orderTotal,
            status: 'en_cours', 
            createdAt: new Date(),
            updatedAt: new Date(),
            userId: userId,
        };
        try {
            let orderIdToUpdate = selectedTable.orderId;
            const isExistingOrder = selectedTable.status === 'occupée' && orderIdToUpdate;
            if (isExistingOrder) {
                const orderRef = doc(db, ordersCollectionPath, orderIdToUpdate);
                await updateDoc(orderRef, orderData);
                console.log("Commande mise à jour:", orderIdToUpdate);
            } else {
                const docRef = await addDoc(collection(db, ordersCollectionPath), orderData);
                orderIdToUpdate = docRef.id;
                console.log("Commande passée:", orderIdToUpdate);
            }
            await updateDoc(doc(db, tablesCollectionPath, selectedTable.id), {
                status: 'occupée',
                orderId: orderIdToUpdate
            });
            showToast(`Commande ${isExistingOrder ? 'mise à jour' : 'passée'} pour la table ${selectedTable.number}!`);
        } catch (e) {
            console.error("Erreur lors de la commande: ", e);
            showToast("Erreur lors de la commande.");
        }
    };

    const handleBillTable = async (table) => {
        if (!table.orderId) {
            showToast("Aucune commande active pour cette table.");
            return;
        }
        try {
            const orderRef = doc(db, ordersCollectionPath, table.orderId);
            const orderSnap = await getDoc(orderRef);
            if (orderSnap.exists()) {
                const orderData = orderSnap.data();
                setBillDetails({
                    items: orderData.items,
                    total: orderData.total,
                    tableNumber: table.number,
                    orderId: table.orderId,
                    tableId: table.id
                });
                setIsBillModalOpen(true);
            } else {
                showToast("Détails de la commande introuvables.");
                await updateDoc(doc(db, tablesCollectionPath, table.id), { status: 'disponible', orderId: null });
            }
        } catch (e) {
            console.error("Erreur facturation:", e);
            showToast("Erreur lors de la préparation de la facture.");
        }
    };

    const handleConfirmPayment = async (orderId, tableId) => {
        try {
            const orderRef = doc(db, ordersCollectionPath, orderId);
            await updateDoc(orderRef, { status: 'payée', updatedAt: new Date() });
            const tableRef = doc(db, tablesCollectionPath, tableId);
            await updateDoc(tableRef, { status: 'facturation' }); 
            setIsBillModalOpen(false);
            setBillDetails(null);
            if(selectedTable && selectedTable.id === tableId) {
                setSelectedTable(prev => ({...prev, status: 'facturation'}));
            }
            showToast("Paiement confirmé. La table est prête à être libérée.");
        } catch (e) {
            console.error("Erreur confirmation paiement:", e);
            showToast("Erreur lors de la confirmation du paiement.");
        }
    };
    
    const handleClearTable = async (tableId) => {
        const tableToClear = tables.find(t => t.id === tableId);
        if (!tableToClear) return;
        const userConfirmed = await new Promise(resolve => {
            if (typeof window !== 'undefined' && window.confirm) {
                resolve(window.confirm(`Êtes-vous sûr de vouloir libérer la Table ${tableToClear.number} ? Ceci est généralement fait après le paiement et le nettoyage.`));
            } else {
                console.warn("window.confirm non disponible. Action annulée.");
                resolve(false);
            }
        });
        if (!userConfirmed) return;
        try {
            const tableRef = doc(db, tablesCollectionPath, tableId);
            await updateDoc(tableRef, { status: 'disponible', orderId: null });
            if (selectedTable && selectedTable.id === tableId) {
                setSelectedTable(null); 
                setCurrentOrder([]);    
            }
            showToast(`Table ${tableToClear.number} libérée.`);
        } catch (e) {
            console.error("Erreur libération table:", e);
            showToast("Erreur lors de la libération de la table.");
        }
    };

    if (isLoading) {
        return <div className="h-screen flex items-center justify-center text-xl font-semibold text-slate-700">Chargement du POS...</div>;
    }
    if (error) { 
        return <div className="h-screen flex items-center justify-center text-red-500 p-4">{error}</div>;
    }

    return (
        <div className="flex h-screen bg-slate-100">
            <nav className="w-20 bg-slate-800 p-4 flex flex-col items-center space-y-6 text-white flex-shrink-0"> 
                <button 
                    onClick={() => setCurrentView('pos')} 
                    title="Point de Vente"
                    className={`p-3 rounded-lg hover:bg-sky-600 transition-colors ${currentView === 'pos' ? 'bg-sky-700' : 'bg-slate-700'}`}
                >
                    <Utensils size={28} />
                </button>
                <button 
                    onClick={() => setCurrentView('admin')} 
                    title="Administration"
                    className={`p-3 rounded-lg hover:bg-sky-600 transition-colors ${currentView === 'admin' ? 'bg-sky-700' : 'bg-slate-700'}`}
                >
                    <Settings size={28} />
                </button>
                <div className="mt-auto">
                    <button onClick={onLogout} title="Déconnexion" className="p-3 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                        <LogOut size={28} />
                    </button>
                </div>
            </nav>

            <main className="flex-1 p-2 md:p-4 overflow-y-auto"> 
                {currentView === 'pos' && (
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 md:gap-4 h-full"> 
                        <div className="lg:col-span-2 h-full flex flex-col gap-2 md:gap-4">
                            <div className="flex-shrink-0 h-[40vh] lg:h-[35%]"> 
                                <TableGrid tables={tables} onSelectTable={handleSelectTable} selectedTableId={selectedTable?.id} onClearTable={handleClearTable} onBillTable={handleBillTable} />
                            </div>
                            <div className="flex-grow min-h-0"> 
                                <MenuDisplay menuItems={menuItems} onAddToOrder={handleAddToOrder} categories={categories} />
                            </div>
                        </div>
                        <div className="lg:col-span-1 h-full"> 
                            <OrderCart 
                                currentOrder={currentOrder} 
                                onUpdateQuantity={handleUpdateQuantity} 
                                onRemoveItem={handleRemoveItem}
                                onPlaceOrder={handlePlaceOrder}
                                selectedTable={selectedTable}
                                tables={tables}
                            />
                        </div>
                    </div>
                )}
                {currentView === 'admin' && (
                     <div className="h-full overflow-y-auto"> 
                        <AdminPanel 
                            userId={userId} 
                            initialMenuItems={menuItems} 
                            initialTablesConfig={ tables.length > 0 ? {count: tables.length, prefix: tables[0]?.number.replace(/[0-9]/g, '') || 'T'} : {count: 10, prefix: 'T'}} 
                        />
                    </div>
                )}
            </main>

            <Modal isOpen={isBillModalOpen} onClose={() => setIsBillModalOpen(false)} title={`Facture Table ${billDetails?.tableNumber}`}>
                {billDetails && (
                    <div>
                        <ul className="mb-4 max-h-60 overflow-y-auto">
                            {billDetails.items.map((item, index) => (
                                <li key={item.id + '-' + index} className="flex justify-between py-1 border-b"> 
                                    <span>{item.name} x {item.quantity}</span>
                                    <span>{(item.price * item.quantity).toFixed(2)} €</span>
                                </li>
                            ))}
                        </ul>
                        <div className="text-right font-bold text-xl mb-4">
                            Total: {billDetails.total.toFixed(2)} €
                        </div>
                        <div className="flex justify-end space-x-3">
                            <button 
                                onClick={() => setIsBillModalOpen(false)}
                                className="px-4 py-2 bg-slate-300 text-slate-800 rounded-md hover:bg-slate-400 transition"
                            >
                                Annuler
                            </button>
                            <button 
                                onClick={() => handleConfirmPayment(billDetails.orderId, billDetails.tableId)}
                                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition"
                            >
                                Confirmer Paiement
                            </button>
                        </div>
                    </div>
                )}
            </Modal>
            {toastMessage && (
                <div className="fixed bottom-4 right-4 bg-slate-800 text-white py-2 px-4 rounded-lg shadow-md transition-opacity duration-300 z-50">
                    {toastMessage}
                </div>
            )}
            <div className="absolute bottom-2 right-2 bg-slate-700 text-white text-xs p-2 rounded shadow-md z-50"> 
                ID Utilisateur: {user.isAnonymous ? 'Anonyme' : userId.substring(0,10)+"..."} <br/> App ID: {appId}
            </div>
        </div>
    );
};


// Composant App principal
export default function App() {
    const [user, setUser] = useState(null); 
    const [isAuthReady, setIsAuthReady] = useState(false);

    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
            console.log("Auth state changed, current user:", currentUser);
            if (currentUser) {
                setUser({ uid: currentUser.uid, email: currentUser.email, isAnonymous: currentUser.isAnonymous });
            } else {
                if (initialAuthToken) {
                    console.log("Tentative de connexion avec custom token...");
                    try {
                        await signInWithCustomToken(auth, initialAuthToken);
                    } catch (error) {
                        console.error("Erreur de connexion avec custom token:", error);
                        try {
                            console.log("Tentative de connexion anonyme (fallback token)...");
                            await signInAnonymously(auth);
                        } catch (anonError) {
                            console.error("Erreur de connexion anonyme (fallback token):", anonError);
                            setUser(null); 
                        }
                    }
                } else {
                     try {
                        console.log("Tentative de connexion anonyme (par défaut)...");
                        await signInAnonymously(auth);
                    } catch (error) {
                        console.error("Erreur de connexion anonyme (par défaut):", error);
                        setUser(null);
                    }
                }
            }
            setIsAuthReady(true);
        });
        return () => unsubscribe(); 
    }, []);


    const handleLogin = (userData) => {
        console.log("Login event (simulé ou via Firebase):", userData);
    };

    const handleLogout = async () => {
        try {
            await signOut(auth);
        } catch (error) {
            console.error("Erreur de déconnexion:", error);
        }
    };

    if (!isAuthReady) {
        return <div className="h-screen flex items-center justify-center text-xl font-semibold text-slate-700">Initialisation de l'authentification...</div>;
    }

    return (
        <div className="font-sans">
            {user ? (
                <DashboardScreen user={user} onLogout={handleLogout} />
            ) : (
                <LoginScreen onLogin={handleLogin} />
            )}
        </div>
    );
}

