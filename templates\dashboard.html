{% extends "base.html" %}

{% block title %}Tableau de bord - POS Restaurant{% endblock %}

{% block content %}
<div class="min-h-screen bg-slate-100">
    <!-- En-tête -->
    <header class="bg-white shadow-sm border-b border-slate-200 px-6 py-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <svg class="icon text-sky-600" viewBox="0 0 24 24">
                    <use href="#icon-utensils"></use>
                </svg>
                <h1 class="text-2xl font-bold text-slate-800">POS Restaurant</h1>
            </div>

            <nav class="flex items-center space-x-4">
                <button id="btn-pos" class="nav-btn active px-4 py-2 rounded-lg font-medium transition-colors bg-sky-600 text-white">
                    <svg class="icon icon-sm inline mr-2" viewBox="0 0 24 24">
                        <use href="#icon-list"></use>
                    </svg>
                    Point de Vente
                </button>
                <button id="btn-admin" class="nav-btn px-4 py-2 rounded-lg font-medium transition-colors text-slate-600 hover:bg-slate-100">
                    <svg class="icon icon-sm inline mr-2" viewBox="0 0 24 24">
                        <use href="#icon-settings"></use>
                    </svg>
                    Administration
                </button>

                <div class="flex items-center space-x-2 ml-6 pl-6 border-l border-slate-200">
                    <svg class="icon icon-sm text-slate-500" viewBox="0 0 24 24">
                        <use href="#icon-user"></use>
                    </svg>
                    <span class="text-sm text-slate-600">{{ user_email }}</span>
                    <a href="{{ url_for('logout') }}" class="text-slate-500 hover:text-slate-700 ml-2">
                        <svg class="icon icon-sm" viewBox="0 0 24 24">
                            <use href="#icon-logout"></use>
                        </svg>
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Contenu principal -->
    <main class="p-6">
        <!-- Vue Point de Vente -->
        <div id="pos-view" class="view-content">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-140px)]">
                <!-- Grille des tables -->
                <div class="lg:col-span-1">
                    <div class="bg-white p-4 rounded-lg shadow h-full overflow-y-auto">
                        <h3 class="text-xl font-semibold mb-3 text-slate-700">Tables</h3>
                        <div id="tables-grid" class="grid grid-cols-2 sm:grid-cols-3 gap-3">
                            <!-- Tables seront chargées ici via JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Menu -->
                <div class="lg:col-span-1">
                    <div class="bg-white p-4 rounded-lg shadow h-full overflow-y-auto">
                        <h3 class="text-xl font-semibold mb-3 text-slate-700">Menu</h3>

                        <!-- Filtres par catégorie -->
                        <div class="mb-4 flex space-x-2 overflow-x-auto pb-2">
                            <button class="category-filter active px-4 py-2 rounded-full text-sm font-medium transition-colors bg-sky-600 text-white" data-category="all">
                                Tous
                            </button>
                            {% for category in categories %}
                            <button class="category-filter px-4 py-2 rounded-full text-sm font-medium transition-colors bg-slate-200 text-slate-700 hover:bg-slate-300 whitespace-nowrap" data-category="{{ category.id }}">
                                {{ category.name }}
                            </button>
                            {% endfor %}
                        </div>

                        <!-- Articles du menu -->
                        <div id="menu-items" class="grid grid-cols-1 gap-4">
                            {% for item in menu_items %}
                            <div class="menu-item border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow" data-category="{{ item.category_id }}">
                                <h4 class="text-md font-semibold text-slate-800">{{ item.name }}</h4>
                                <p class="text-sm text-slate-600">{{ item.description or 'Pas de description' }}</p>
                                <p class="text-lg font-bold text-sky-600 my-2">{{ "%.2f"|format(item.price) }} €</p>
                                <button onclick="addToOrder({{ item.id }}, '{{ item.name }}', {{ item.price }})"
                                        class="w-full bg-sky-500 hover:bg-sky-600 text-white text-sm py-2 px-3 rounded-md transition-colors flex items-center justify-center">
                                    <svg class="icon icon-sm mr-1" viewBox="0 0 24 24">
                                        <use href="#icon-plus"></use>
                                    </svg>
                                    Ajouter
                                </button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Panier de commande -->
                <div class="lg:col-span-1">
                    <div class="bg-white p-4 rounded-lg shadow h-full flex flex-col">
                        <h3 class="text-xl font-semibold mb-3 text-slate-700">Commande</h3>

                        <div id="selected-table-info" class="mb-3 hidden">
                            <p class="text-sm text-slate-600">Table sélectionnée: <span id="selected-table-number" class="font-semibold"></span></p>
                        </div>

                        <div id="no-table-selected" class="flex-grow flex flex-col justify-center items-center">
                            <svg class="icon icon-lg text-slate-400 mb-4" viewBox="0 0 24 24">
                                <use href="#icon-kanban"></use>
                            </svg>
                            <p class="text-slate-600 text-center">Veuillez sélectionner une table pour commencer une commande.</p>
                        </div>

                        <div id="order-content" class="hidden flex-grow flex flex-col">
                            <div id="order-items" class="flex-grow overflow-y-auto pr-1">
                                <!-- Articles de la commande seront ajoutés ici -->
                            </div>

                            <div class="mt-auto pt-4 border-t border-slate-200">
                                <div class="flex justify-between items-center text-lg font-semibold mb-3">
                                    <span>Total:</span>
                                    <span id="order-total" class="text-sky-600">0.00 €</span>
                                </div>
                                <button id="place-order-btn" onclick="placeOrder()"
                                        class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md transition-colors disabled:opacity-50">
                                    Passer la commande
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vue Administration -->
        <div id="admin-view" class="view-content hidden">
            <div class="max-w-4xl mx-auto space-y-8">
                <h2 class="text-2xl font-semibold text-slate-800">Panneau d'Administration</h2>

                <!-- Section Gestion du Menu -->
                <section class="p-6 bg-white rounded-lg shadow">
                    <h3 class="text-xl font-semibold mb-4 text-sky-700">Gérer le Menu</h3>

                    <form id="add-menu-item-form" class="space-y-4 mb-6">
                        <input type="text" id="item-name" placeholder="Nom de l'article" class="w-full p-2 border rounded-md" required />
                        <input type="number" id="item-price" placeholder="Prix (€)" class="w-full p-2 border rounded-md" step="0.01" required />
                        <select id="item-category" class="w-full p-2 border rounded-md" required>
                            <option value="">Choisir catégorie</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="relative">
                            <textarea id="item-description" placeholder="Description (optionnel)" class="w-full p-2 border rounded-md min-h-[80px]"></textarea>
                            <button type="button" id="generate-description-btn"
                                    class="absolute top-2 right-2 bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded-md text-xs flex items-center disabled:opacity-50 transition-colors"
                                    title="Nécessite une clé API Gemini configurée">
                                <svg class="icon icon-sm mr-1" viewBox="0 0 24 24">
                                    <use href="#icon-sparkles"></use>
                                </svg>
                                ✨ Générer Description
                            </button>
                        </div>
                        <button type="submit" class="w-full bg-sky-600 text-white py-2 px-4 rounded-md hover:bg-sky-700 transition">
                            Ajouter l'article
                        </button>
                    </form>

                    <div id="menu-items-list" class="max-h-60 overflow-y-auto">
                        <!-- Liste des articles sera chargée ici -->
                    </div>
                </section>
            </div>
        </div>
    </main>
</div>

<!-- Modal de facturation -->
<div id="bill-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 hidden">
    <div class="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold">Facture</h3>
            <button onclick="closeBillModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
        </div>
        <div id="bill-content">
            <!-- Contenu de la facture sera ajouté ici -->
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/pos.js') }}"></script>
{% endblock %}
