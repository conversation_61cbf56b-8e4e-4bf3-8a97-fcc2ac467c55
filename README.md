# POS Restaurant - Version Flask

Application de point de vente (POS) pour restaurant convertie de React/Firebase vers Python Flask avec SQLAlchemy et SQLite.

## Fonctionnalités

### Point de Vente
- **Gestion des tables** : Visualisation et sélection des tables avec statuts (disponible, occupée, facturation)
- **Menu interactif** : Affichage du menu avec filtrage par catégories
- **Gestion des commandes** : Ajout d'articles, modification des quantités, passage de commandes
- **Facturation** : Génération de factures et gestion des paiements
- **Libération des tables** : Remise à zéro des tables après paiement

### Administration
- **Gestion du menu** : Ajout, modification et suppression d'articles
- **Gestion des catégories** : Organisation du menu par catégories
- **Génération de descriptions** : Utilisation de l'API Gemini pour générer des descriptions automatiques (optionnel)

## Installation

1. **<PERSON><PERSON><PERSON> le projet** (ou utiliser les fichiers existants)

2. **Installer les dépendances Python** :
   ```bash
   pip install -r requirements.txt
   ```

3. **Configuration de l'application** :
   ```bash
   # Copiez le fichier d'exemple
   cp .env_exemple .env

   # Ou sur Windows
   copy .env_exemple .env
   ```

   Puis éditez le fichier `.env` et ajoutez votre clé API Gemini :
   ```
   GEMINI_API_KEY=votre_cle_api_gemini_ici
   ```

   **Pour obtenir une clé API Gemini gratuite** :
   - Allez sur [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Connectez-vous avec votre compte Google
   - Cliquez sur "Create API Key"
   - Copiez la clé dans votre fichier `.env`

4. **Lancer l'application** :
   ```bash
   python app.py
   ```

5. **Accéder à l'application** :
   Ouvrez votre navigateur et allez à `http://localhost:5000`

## Utilisation

### Première connexion
- Entrez n'importe quel email et mot de passe
- Un compte sera créé automatiquement avec :
  - 3 catégories par défaut (Plats, Boissons, Desserts)
  - 10 tables par défaut (T1 à T10)

### Point de Vente
1. **Sélectionner une table** : Cliquez sur une table disponible (verte)
2. **Ajouter des articles** : Filtrez par catégorie et cliquez sur "Ajouter"
3. **Modifier la commande** : Ajustez les quantités ou supprimez des articles
4. **Passer la commande** : Cliquez sur "Passer la commande"
5. **Facturer** : Cliquez sur "Facturer" sur une table occupée (orange)
6. **Libérer** : Cliquez sur "Libérer" sur une table en facturation (rouge)

### Administration
1. **Ajouter un article** : Remplissez le formulaire et cliquez sur "Ajouter l'article"
2. **Générer une description** : Utilisez le bouton "✨ Générer Description" (nécessite une clé API Gemini)
3. **Supprimer un article** : Cliquez sur l'icône poubelle à côté de l'article

## Structure du projet

```
pos_ai_3/
├── app.py                 # Application Flask principale
├── requirements.txt       # Dépendances Python
├── README.md             # Ce fichier
├── templates/            # Templates HTML
│   ├── base.html         # Template de base
│   ├── login.html        # Page de connexion
│   └── dashboard.html    # Interface principale
└── static/
    └── js/
        └── pos.js        # JavaScript pour l'interactivité
```

## Base de données

L'application utilise SQLite avec les tables suivantes :
- `user` : Utilisateurs
- `category` : Catégories du menu
- `menu_item` : Articles du menu
- `table` : Tables du restaurant
- `order` : Commandes
- `order_item` : Articles des commandes

La base de données est créée automatiquement au premier lancement.

## Technologies utilisées

- **Backend** : Python Flask, SQLAlchemy, SQLite
- **Frontend** : HTML, Tailwind CSS, JavaScript (minimal)
- **API externe** : Google Gemini AI (optionnel)

## Différences avec la version React/Firebase

### Supprimé
- Firebase/Firestore (remplacé par SQLite)
- React/TypeScript (remplacé par HTML/JavaScript)
- Authentification Firebase (remplacé par sessions Flask simples)
- Dépendances Node.js

### Conservé
- Toutes les fonctionnalités principales
- Interface utilisateur similaire avec Tailwind CSS
- Intégration API Gemini (optionnelle)
- Icônes SVG intégrées

### Amélioré
- Base de données relationnelle plus robuste
- Authentification simplifiée
- Déploiement plus simple (un seul serveur)
- Moins de dépendances externes

## Notes

- L'authentification est simplifiée pour la démo (pas de vérification de mot de passe)
- L'API Gemini est optionnelle et nécessite une clé API
- La base de données SQLite est stockée localement dans `pos_restaurant.db`
- L'application est configurée en mode debug pour le développement
